package com.ifallious.utils.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import gg.essential.universal.UChat;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.List;

public class BlockRenderer {
    // Inner class to store block rendering information
    public static class BlockRenderInfo {
        public final BlockPos pos;
        public final Color sideColor;
        public final Color lineColor;
        public final ShapeMode shapeMode;
        public final int excludedDirections;

        public BlockRenderInfo(BlockPos pos, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
            this.pos = pos;
            this.sideColor = sideColor;
            this.lineColor = lineColor;
            this.shapeMode = shapeMode;
            this.excludedDirections = excludedDirections;
        }

        public BlockRenderInfo(BlockPos pos, Color sideColor, Color lineColor, ShapeMode shapeMode) {
            this(pos, sideColor, lineColor, shapeMode, 0);
        }
    }

    // Store the blocks with their rendering parameters
    private final List<BlockRenderInfo> blocksToRender = new ArrayList<>();
    
    public BlockRenderer() {
        GlobalEventBus.subscribe(this);
    }

    // Method to add a block with default rendering parameters (red color, both shape mode)
    public void addBlock(int x, int y, int z) {
        addBlock(x, y, z, new Color(255, 0, 0, 50), new Color(255, 0, 0, 255), ShapeMode.Both);
    }

    /**
     *
     * @param x
     * @param y
     * @param z
     * @param sideColor
     * @param lineColor
     * @param shapeMode
     */
    public void addBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode) {
        addBlock(x, y, z, sideColor, lineColor, shapeMode, 0);
    }

    // Method to add a block with full customization
    public void addBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
        BlockPos pos = new BlockPos(x, y, z);
        BlockRenderInfo info = new BlockRenderInfo(pos, sideColor, lineColor, shapeMode, excludedDirections);
        blocksToRender.add(info);
    }

    // Method to add a block using BlockRenderInfo directly
    public void addBlock(BlockRenderInfo blockInfo) {
        blocksToRender.add(blockInfo);
    }

    // Method to remove a block by position
    public void removeBlock(int x, int y, int z) {
        BlockPos targetPos = new BlockPos(x, y, z);
        blocksToRender.removeIf(info -> info.pos.equals(targetPos));
    }

    // Method to remove a specific BlockRenderInfo
    public void removeBlock(BlockRenderInfo blockInfo) {
        blocksToRender.remove(blockInfo);
    }

    // Method to clear all blocks
    public void clearBlocks() {
        blocksToRender.clear();
    }

    // Method to get all blocks (useful for inspection/debugging)
    public List<BlockRenderInfo> getBlocks() {
        return new ArrayList<>(blocksToRender);
    }

    // Method to update a block's rendering parameters
    public boolean updateBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode) {
        return updateBlock(x, y, z, sideColor, lineColor, shapeMode, 0);
    }

    // Method to update a block's rendering parameters with excluded directions
    public boolean updateBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
        BlockPos targetPos = new BlockPos(x, y, z);
        for (int i = 0; i < blocksToRender.size(); i++) {
            BlockRenderInfo info = blocksToRender.get(i);
            if (info.pos.equals(targetPos)) {
                blocksToRender.set(i, new BlockRenderInfo(targetPos, sideColor, lineColor, shapeMode, excludedDirections));
                return true;
            }
        }
        return false; // Block not found
    }
    
    @EventHandler
    private void onRender(Render3DEvent event) {
        UChat.chat("Render event: " + event.getClass().getSimpleName());
        // This is where the magic happens - draw each block with its custom parameters
        for (BlockRenderInfo info : blocksToRender) {
            // Draw a box at the block position using the block's specific rendering parameters
            event.renderer.box(
                info.pos.getX(), info.pos.getY(), info.pos.getZ(),           // Start coordinates
                info.pos.getX() + 1, info.pos.getY() + 1, info.pos.getZ() + 1, // End coordinates
                info.sideColor,                                              // Custom side color
                info.lineColor,                                              // Custom line color
                info.shapeMode,                                              // Custom shape mode
                info.excludedDirections                                      // Custom excluded directions
            );
        }
    }
}