package com.ifallious.utils;

import com.ifallious.features.guild.AskCommand;
import com.ifallious.features.guild.GuildRecruiter;
import com.ifallious.features.guild.InviteClient;
import com.ifallious.features.raids.*;
import com.ifallious.features.render.GrappleHook;
import com.ifallious.features.rmt.*;
import com.ifallious.features.spells.*;
import com.ifallious.features.war.*;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.featureutils.TrapUtils;
import com.ifallious.utils.minecraft.ChatHandler;
import com.ifallious.utils.minecraft.ParticleUtils;
import com.ifallious.utils.minecraft.Tick;
import com.ifallious.utils.render.BlockRenderer;
import com.ifallious.utils.render.FabricRenderHandler;

/**
 * Initializes all mod components
 */
public class Initializer {
    /**
     * Initialize all mod components and features
     * Loads configuration and instantiates all feature classes
     */
    public static void init() {

        ConfigManager.loadConfig();
        new BlockRenderer();
        new Spellmacro();
        new AuraDodge();
        new AutoRadiance();
        new ShadowProjection();
        new TotemCast();
        new RareMobTracker();
        new CorruptedReminder();
        new Tick();
        new StrongerCharge();
        new TrapUtils();
        new TrapTimer();
        new TCCExitLocator();
        FabricRenderHandler.initialize();
        new Updater();
        new ParticleUtils();
        new AutoTransfer();
        new ChatHandler();
        new AskCommand();
        new RaidCommand();
        GuildRecruiter.registerCommand();
        new InviteClient().start();
        new GrappleHook();

    }
}
